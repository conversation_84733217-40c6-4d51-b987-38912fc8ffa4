# Created by <PERSON><PERSON> at 2022/12/05

Feature:   EDA 相关happy path

  @ui @wechatwork @Android @Android_536714465 @no_daily @GQ @EDA @happy_path @Abbvie
  Scenario: 【Abbvie+BMS】先创建拜访后EDA happy path
    Given 开始批量更新元数据
    Given 按如下条件{"id":"6dc9614d-266e-4769-b3a1-41520fdf54c6","page_list_id":"7c1399dd-5b17-4242-9aa2-27334a30eb4b"}更新表page_list_fields中的字段值{"display_mode":"cover"}
    Then 按如下条件{"id":"7c1399dd-5b17-4242-9aa2-27334a30eb4b"}更新表page_lists中的字段值{"mode":"None"}
    Given 结束批量更新元数据
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的拜访"
    And 在安卓设备点击"新建"
    And 在安卓设备点击"会后拜访"
    Then 用户填写如下数据在我的拜访页面
      | label | value               |
      | 拜访医生  | {hcp1_account_name} |
      | 拜访日期  | 当天                  |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"(//a[text()='电子资料列表'])[last()]"
    And 代表搜索电子资料"代表一次性问卷的浏览与填写"
    Then 医药代表从sfa企业微信端检查电子资料"代表一次性问卷的浏览与填写"是否存在"yes"
    And 在安卓设备点击删除图标清空搜索框
    Then 医药代表从sfa企业微信端页面点击"资料类型"
    Given 并使用过滤如下条件在侧边栏资料列表界面
      | key  | value |
      | 资料类型 | IT相关  |
    Then 在企业微信端检验"资料类型"图标"高亮"
    Then 医药代表从sfa企业微信端检查电子资料"automation-ppt"是否存在"筛选yes"
    Then 验证电子资料列表
      | 电子资料名称         | 缩略图 |
      | automation-pdf | yes |
    Then 医药代表从sfa企业微信端页面点击"资料类型"
    Then 医药代表从sfa企业微信端页面点击"重置"
    Then 在企业微信端检验"资料类型"图标"不高亮"

###    Then 医药代表从sfa企业微信端检查电子资料"代表一次性问卷的浏览与填写"是否存在"筛选yes"
    Then 医药代表从sfa企业微信端检查电子资料"ATE1-每日维生素E36粒-渠道会议"是否存在"筛选yes"
    Then 医药代表从sfa企业微信端页面点击"排序"
    Given 并使用过滤如下条件在侧边栏资料列表界面
      | key    | value |
      | 实际发布日期 | 升序    |
    Then 医药代表从sfa企业微信端检查侧边栏电子资料列表页按日期"升序"排列

  @ui @wechatwork @Android @Android_536716279 @no_daily @GQ @EDA @happy_path @Abbvie
  Scenario: 【Abbvie+BMS】资料列表页happy path
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的拜访"
#    Then 在我的拜访页面打开最新一个拜访
    And 在安卓设备点击"新建"
    And 在安卓设备点击"会后拜访"
    Then 用户填写如下数据在我的拜访页面
      | label | value               |
      | 拜访医生  | {hcp1_account_name} |
      | 拜访日期  | 当天                  |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-ppt"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    Then 医药代表从sfa企业微信端检查播放资料页面的相关信息及展示
      | element    | existed |
      | 播放时间状态栏    | yes     |
      | 保存拜访报告控制按钮 | yes     |
      | 资料详情       | yes     |
    Then 医药代表从sfa企业微信端资料讲解页面校验保存拜访报告在点击弹出后出现
    Then 医药代表从sfa企业微信端资料讲解页面校验保存拜访报告在等待5s后消失
    Then 验证多页电子资料显示左右翻页按钮和页码后保留在当前页面
    Then 验证多页电子资料automation-ppt左右翻页后每页只生成一条记录
    Then 医药代表从sfa企业微信端资料讲解页面点击保存拜访报告
#    Then 验证电子资料详情页面
#      | 相关产品 | 压氏立7片;立文降压14片;立文降压7片 |
#      | 性质   | 私有                   |
#    Then 验证页面出现"讲解结束，已自动为您更新拜访报告"
#    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                                  | 拜访产品                 |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;立文降压7片::None;压氏立7片::要测一下关键信息能不能工作-Auto | 压氏立7片;立文降压14片;立文降压7片 |


  @ui @wechatwork @Android @Android_536724594 @no_daily @GQ @EDA @happy_path @Abbvie
  Scenario: 【Abbvie+BMS】资料详情页播放电子资料happy path
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的拜访"
#    Then 在我的拜访页面打开最新一个拜访
    And 在安卓设备点击"新建"
    And 在安卓设备点击"会后拜访"
    Then 用户填写如下数据在我的拜访页面
      | label | value               |
      | 拜访医生  | {hcp1_account_name} |
      | 拜访日期  | 当天                  |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-ppt"
    Then 代表点击电子资料"automation-ppt"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过

    Then 医药代表从sfa企业微信端检查播放资料页面的相关信息及展示
      | element    | existed |
      | 播放时间状态栏    | yes     |
      | 保存拜访报告控制按钮 | yes     |
      | 资料详情       | yes     |
    Then 医药代表从sfa企业微信端资料讲解页面校验保存拜访报告在点击弹出后出现
    Then 医药代表从sfa企业微信端资料讲解页面校验保存拜访报告在等待5s后消失
    Then 验证多页电子资料显示左右翻页按钮和页码后保留在当前页面
    Then 验证多页电子资料automation-ppt左右翻页后每页只生成一条记录
    Then 医药代表从sfa企业微信端资料讲解页面点击保存拜访报告
#    Then 验证电子资料详情页面
#      | 相关产品 | 压氏立7片;立文降压14片;立文降压7片 |
#      | 性质   | 私有                   |
#    Then 验证页面出现"讲解结束，已自动为您更新拜访报告"
#    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                                  | 拜访产品                 |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;立文降压7片::None;压氏立7片::要测一下关键信息能不能工作-Auto | 压氏立7片;立文降压14片;立文降压7片 |


  @ui @wechatwork @Android @Android_537136895 @Android_538245364 @no_daily @Jesse @happy_path @group1 @tencent_meeting @Merck
  Scenario: 【Merck】eDA event Happy Path -- Merge with C538245364 | 腾讯会议自动回收及eDA播放的happy path
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    When 清理医生绑定信息微信昵称{hcp1_old_open_id}
    When 清理医生绑定信息微信昵称{hcp3_old_open_id}
    When 清理医生绑定信息微信昵称{hcp1_nick_name}
    When 清理医生绑定信息微信昵称{hcp3_nick_name}
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 恢复医生绑定信息微信昵称{hcp3_nick_name}
    When 清理医生绑定信息微信昵称{hcp1_old_union_id}
    When 恢复新线服务号医生绑定信息微信昵称{hcp1_nick_name}
    When 清理医生绑定信息微信昵称{hcp3_old_union_id}
    When 恢复新线服务号医生绑定信息微信昵称{hcp3_nick_name}

    Given 设置custom setting
      | key                                       | value      |
      | eda_need_feedback                         | true       |
#      | enable_multichannel_service               | false      |
      | event_valid_presence_duration_ratio       | {"all": 1} |
      | multichannel_identity_flow_configurations | {}         |
        And 调用jenkins DevCanisRefreshCustomInfo构建任务
    """
    {
        "Tenant": "qacommondl",
        "RefreshTemplate": true
        }
    """
    Given "admin""<EMAIL>"登录系统
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为立文降压12片
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为立文降压14片
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为立文降压7片
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为压氏立7片
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为立文系列
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为每日维生素E48粒
    And [rtw] 在current_month岗位版本中给岗位{rep1_sfa_gangweimingcheng}分配岗位产品为压氏立14片
    And 用户登出系统
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    And 医生1打开微信添加医药代表1到我的企业微信联系人
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的活动"
    And 在安卓设备点击"新建"
    And 在安卓设备点击"城市会"
    And 用户填写如下数据在我的活动页面
      | label    | value                  |
      | 活动名称     | 城市会{rand}              |
      | 计划活动开始时间 | {now}                  |
      | 计划活动结束时间 | {now+10m}              |
      | 活动目的     | 新产品介绍                  |
      | 参会人等级    | 全选                     |
      | 主要产品     | 立文降压12片                |
      | 会议资料     | automation-onefile-pdf |
      | 你问我答     | 你问我答测试                 |
      | 是否为视频会议  | 远程会议                   |
#      | 会议类别     | 远程会议                   |
      | 会议平台     | 腾讯会议                   |
      | 是否是重要活动  | 否                      |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"参会人-"
    And 在安卓设备点击"添加客户"如果没有则跳过
    And 在安卓设备点击"新建"如果没有则跳过
    And 添加参会客户
      | label               | value |
      | {hcp1_account_name} | 选中    |
      | {hcp3_account_name} | 选中    |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成

    Given [rtw]  "rep""{rep1_sfa_name}"登录系统
    And 获取活动名称"城市会{rand}"id存放到上下文context.record_id
    When 对会议的sub_tab页account参会人["{hcp1_account_name}","{hcp3_account_name}"]批量add签到，object_name是event_account，page_list是参会医生tab列表，可在table提供参数值
    And 用户登出系统

    And 在安卓设备点击"基本信息"
    And 在安卓设备点击"更多"

    And 在安卓设备点击"提交审批"
    And 在安卓设备点击"继续提交"
    And 在安卓设备选中勾选框"阅读并同意此声明"
    And 用户填写如下数据在我的活动页面
      | label | value |
      | 请填写留言 | 城市会测试 |
    And 在安卓设备点击"确认"

   Given "dsm""<EMAIL>"登录系统
    And 审批人审批通过被审批人提交的event
    Then 验证返回的状态码是200
    And 用户登出系统

    Given 用户{rep1_sfa_name}登录online页面不启用无痕
    Then 用户在online端主页左侧菜单点击活动管理
    Then 用户在online端列表过滤活动名称等于城市会{rand}查看第1行
    Then 用户在online端点击"开始开会"
    And 切换浏览器窗口

   When 系统切换到安卓设备 "androidDevice_1_wechatwork" 进入之前的页面
   And 系统切换到webview界面
    # When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    # Then 医药代表从企业微信端打开SFA应用
    # Then 系统切换到webview界面
    # Then 医药代表从sfa企业微信端页面点击"我的活动"
    # And 在安卓设备按如下条件筛选列表
    #   | label | operator | value          |
    #   | 名称    | 等于       | 城市会{rand} |
    # And 在企业微信列表中点击"城市会{rand}"

    And 在医学活动详情页面展开下拉标签面板

    And 在安卓设备点击"邀请二维码"
    And 在安卓设备点击"发送邀请码"
    Then 系统切换到native_app界面
    And 在安卓设备转发当前页给"{hcp1_nick_name}"
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生从微信端等待1条好友{rep1_nick_name}发来的新消息
    Then 医生从微信端打开医药代表好友{rep1_nick_name_with_corp_wechat}的聊天窗口

    And 医生1打开微信最后一条消息并同意服务条款

    And 在安卓设备退出app"腾讯会议"
    And 在安卓设备新线邀请函点击"点击加入会议"

    When 系统切换到安卓设备 "androidDevice_1_wechatwork" 进入之前的页面
    And 系统切换到webview界面
    And 在安卓设备等待页面加载完成
    And 返回前一页
    And 在安卓设备等待页面加载完成
    And 在医学活动详情页面展开下拉标签面板

    And 在安卓设备点击"基本信息"
    And 在安卓设备点击"更多"
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-ppt"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"选择其他资料"
    And 代表搜索电子资料"automation-pdf"
    Then 代表点击电子资料"automation-pdf"卡片进入电子资料详情
    Then 在安卓设备电子资料右翻1页
    Then 验证在播放EDA页面Duration在增加
    Then 在播放EDA页面Feedback积极
    Then 在安卓设备电子资料右翻1页
    Then 验证在播放EDA页面Duration在增加
    Then 在播放EDA页面Feedback消极
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    Then 验证医学活动详情
      | 会议资料                                                 |
      | automation-ppt;automation-pdf;automation-onefile-pdf |
    And 在安卓设备点击"生成拜访报告"
    And 在安卓设备点击"确定"

    # And 在Mac app退出"腾讯会议"
    And 在Mac app点击"结束会议"
    And 验证数据库表remote_meetings中，最新一条数据字段attendee_job_status_id值为NULL
    ### 测试结果Remote Meeting.state =ready_to_start
    And 验证数据库表remote_meetings中，最新一条数据字段state_id值为70039914-a4b5-4aac-9904-bed3754ab7b0
    And 等待腾讯会议结束
    And 等待60秒

    And 验证数据库表remote_meetings中，最新一条数据字段attendee_job_status_id值为NULL
    ### Remote Meeting.state = end
    And 验证数据库表remote_meetings中，最新一条数据字段state_id值为0f335361-1baa-4db5-8299-d64803fb94f0
#    And 通过置空ETA字段值来触发remote_meeting名称"城市会{rand}"相关jobs
#    And 等待300秒
     And 手工触发腾讯会议定时任务

    And 在安卓设备点击"生成拜访报告"
       Then 用户填写如下数据在我的拜访页面
      | label | value               |
      | 拜访医生  | {hcp1_account_name} |

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成

    Then 验证拜访报告详情
      | 拜访医生                 | 拜访日期    | 关键信息                                                                  | 拜访产品                 | 相关电子资料                        | 用户反馈                                                                                        |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;立文降压7片::None;压氏立7片::要测一下关键信息能不能工作-Auto | 立文降压14片;立文降压7片;压氏立7片 | automation-ppt;automation-pdf | automation-ppt:第1页:None;automation-pdf:第1页:None;automation-pdf:第2页:积极;automation-pdf:第3页:消极 |
    And 在安卓设备记录veeva code
    And 返回sfa企业微信首页
    And 在安卓设备点击"我的活动"
    And 在安卓设备按如下条件筛选列表
      | label | operator | value     |
      | 名称    | 等于       | 城市会{rand} |
    And 在企业微信列表中点击"城市会{rand}"
    And 在安卓设备点击"更多"
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"同步html-2" 
    Then 代表点击电子资料"同步html-2"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 在安卓设备等待页面加载完成
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"更多"
    And 在安卓设备点击"更新拜访报告"
    And 在安卓设备点击"确定"
    And 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
    |拜访报告已更新，前往查看|
    And 在安卓设备等待页面加载完成
    Then 验证拜访报告详情
      | 拜访医生                 | 拜访日期    | 关键信息                                                                                                | 拜访产品                                   | 相关电子资料                                 |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;每日维生素E48粒::None;立文降压7片::None;压氏立7片::要测一下关键信息能不能工作-Auto;立文降压12片::None | 立文降压14片;立文降压7片;压氏立7片;立文降压12片;每日维生素E48粒 | automation-ppt;automation-pdf;同步html-2 |

  @ui @wechatwork @Android @Android_537071095 @no_daily @Jesse @Abbvie
  Scenario: 【Abbvie+BMS】event有审批资料，再播放eDA
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的活动"
    And 在安卓设备点击"新建"
    And 在安卓设备点击"城市会"
    And 用户填写如下数据在我的活动页面
      | label    | value                           |
      | 活动名称     | 城市会{rand}                       |
      | 计划活动开始时间 | {now}                           |
      | 计划活动结束时间 | {now+10m}                       |
      | 活动目的     | 新产品介绍                           |
      | 参会人等级    | 全选                              |
      | 主要产品     | 立文降压14片                         |
      | 会议资料     | automation-onefile-pdf;多页PDF原文件 |
      | 你问我答     | 你问我答测试                          |
      | 是否为视频会议  | 常规会议                            |
      | 是否是重要活动  | 否                               |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"参会人-"
    And 在安卓设备点击"添加客户"如果没有则跳过
    And 在安卓设备点击"新建"如果没有则跳过
    And 添加参会客户
      | label               | value |
      | {hcp1_account_name} | 选中    |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"基本信息"
    And 在安卓设备点击"更多"
    And 在安卓设备点击"提交审批"
    And 在安卓设备点击"继续提交"
    And 在安卓设备点击"确认"
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-ppt"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 记录当前时间到hcp1_read_datetime
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒
    And 在安卓设备点击"选择其他资料"
    And 代表搜索电子资料"automation-pdf"
    Then 代表点击电子资料"automation-pdf"卡片进入电子资料详情
    And 记录当前时间到hcp1_close_datetime
    Then 验证electronic_document_tracking_sessions表没有新生成一条Tracking Session
      | hcp_nick_name    | open_datetime        | electronic_document_id | created_by        |
      | {hcp1_nick_name} | {hcp1_read_datetime} | automation-onefile-pdf | {rep1_sfa_userid} |
    Then 验证electronic_document_tracking_sessions表没有新生成一条Tracking Session
      | hcp_nick_name    | open_datetime        | electronic_document_id | created_by        |
      | {hcp1_nick_name} | {hcp1_read_datetime} | 多页PDF原文件               | {rep1_sfa_userid} |

    Then 验证electronic_document_tracking_sessions表新生成一条Tracking Session
      | hcp_nick_name    | wechat_external_id | electronic_document_id | open_id        | account_id        | open_datetime        | close_datetime        | channel_value        | record_type_id                       | from_channel_value   | created_by        |
      | {hcp1_nick_name} | ID                 | automation-ppt         | {hcp1_open_id} | {hcp1_account_id} | {hcp1_read_datetime} | {hcp1_close_datetime} | {hcp1_channel_value} | 3e3219ba-4dee-40ff-bab3-a9e1cf4a58e2 | {hcp1_channel_value} | {rep1_sfa_userid} |
    And 记录当前时间到hcp1_read_datetime
    Then 在安卓设备电子资料右翻1页

    Then 在安卓设备电子资料右翻1页

    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 记录当前时间到hcp1_close_datetime
    Then 验证医学活动详情
      | 会议资料                                                          |
      | automation-ppt;automation-pdf;automation-onefile-pdf;多页PDF原文件 |

    Then 验证electronic_document_tracking_sessions表没有新生成一条Tracking Session
      | hcp_nick_name    | open_datetime        | electronic_document_id | created_by        |
      | {hcp1_nick_name} | {hcp1_read_datetime} | automation-onefile-pdf | {rep1_sfa_userid} |
    Then 验证electronic_document_tracking_sessions表没有新生成一条Tracking Session
      | hcp_nick_name    | open_datetime        | electronic_document_id | created_by        |
      | {hcp1_nick_name} | {hcp1_read_datetime} | 多页PDF原文件               | {rep1_sfa_userid} |
    Then 验证electronic_document_tracking_sessions表新生成一条Tracking Session
      | hcp_nick_name    | wechat_external_id | electronic_document_id | open_id        | account_id        | open_datetime        | close_datetime        | channel_value        | record_type_id                       | from_channel_value   | created_by        |
      | {hcp1_nick_name} | ID                 | automation-pdf         | {hcp1_open_id} | {hcp1_account_id} | {hcp1_read_datetime} | {hcp1_close_datetime} | {hcp1_channel_value} | 3e3219ba-4dee-40ff-bab3-a9e1cf4a58e2 | {hcp1_channel_value} | {rep1_sfa_userid} |
########### 去重验证 验证在View Mode，此会议资料不会去重。
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-pdf"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 记录当前时间到hcp1_read_datetime
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 记录当前时间到hcp1_close_datetime
    Then 验证electronic_document_tracking_sessions表新生成一条Tracking Session
      | hcp_nick_name    | wechat_external_id | electronic_document_id | open_id        | account_id        | open_datetime        | close_datetime        | channel_value        | record_type_id                       | from_channel_value   | created_by        |
      | {hcp1_nick_name} | ID                 | automation-pdf         | {hcp1_open_id} | {hcp1_account_id} | {hcp1_read_datetime} | {hcp1_close_datetime} | {hcp1_channel_value} | 3e3219ba-4dee-40ff-bab3-a9e1cf4a58e2 | {hcp1_channel_value} | {rep1_sfa_userid} |

    Then 验证医学活动详情
      | 会议资料                                                                         |
      | automation-ppt;automation-pdf;automation-pdf;automation-onefile-pdf;多页PDF原文件 |
    And 在安卓设备点击"更多"
    And 在安卓设备点击"编辑"
    Then 验证医学活动详情
      | 会议资料                                                          |
      | automation-ppt;automation-pdf;automation-onefile-pdf;多页PDF原文件 |
    And 在医学活动详情页面删除会议资料
      | 会议资料                            |
      | automation-onefile-pdf;多页PDF原文件 |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-onefile-pdf"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"选择其他资料"
    And 代表搜索电子资料"automation-ppt"
    Then 代表点击电子资料"automation-ppt"卡片进入电子资料详情
    Then 在安卓设备电子资料右翻1页
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    Then 验证医学活动详情
      | 会议资料                                                                               |
      | automation-ppt;automation-pdf;automation-pdf;automation-onefile-pdf;automation-ppt |

    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"我的活动"
    And 在安卓设备点击"新建"
    And 在安卓设备点击"城市会"
    And 用户填写如下数据在我的活动页面
      | label    | value                           |
      | 活动名称     | 城市会{rand}                       |
      | 计划活动开始时间 | {now}                           |
      | 计划活动结束时间 | {now+10m}                       |
      | 活动目的     | 新产品介绍                           |
      | 参会人等级    | 全选                              |
      | 主要产品     | 立文降压14片                         |
      | 会议资料     | automation-onefile-pdf;多页PDF原文件 |
      | 你问我答     | 你问我答测试                          |
      | 是否为视频会议  | 常规会议                            |
      | 是否是重要活动  | 否                               |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"参会人-"
    And 在安卓设备点击"添加客户"如果没有则跳过
    And 在安卓设备点击"新建"如果没有则跳过
    And 添加参会客户
      | label               | value |
      | {hcp1_account_name} | 选中    |
    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"基本信息"
    And 在安卓设备点击"更多"
    And 在安卓设备点击"提交审批"
    And 在安卓设备点击"继续提交"
    And 在安卓设备点击"确认"
    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-ppt"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成

    And 在安卓设备点击"播放资料"
    And 代表搜索电子资料"automation-onefile-pdf"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"确定"如果没有则跳过
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    And 在安卓设备等待页面加载完成
    Then 验证医学活动详情
      | 会议资料                                           |
      | automation-ppt;automation-onefile-pdf;多页PDF原文件 |

  @ui @wechatwork @Android @Android_536981914 @no_daily @Jesse @EDA @Abbvie @Android_537571127
  Scenario: 【Abbvie】list和Detail页配置播放资料button时， 电子资料的预览
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"},{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"}],\"direct-presentation\":false}"}
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    Then 验证电子资料列表页每个资料后面都会显示"播放资料"的按钮
    Then 验证电子资料列表页每个资料后面都会显示"预览资料"的按钮
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"更多"
    And 在安卓设备点击"预览资料"
    And 验证代表可以切换页面
    And 返回前一页
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 代表在资料详情页打开预览资料
    And 验证代表可以切换页面
    And 返回前一页
    And 在安卓设备等待页面加载完成
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"区域医生"
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    And 验证代表可以切换页面

  @ui @wechatwork @Android @Android_537102842 @no_daily @Jesse @Abbvie
  Scenario: [Abbvie]ORI--61431电子资料播放，触发trigger限制报错，trigger返回的报错信息没法提示出来
    Given 开始批量更新元数据
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"},{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"},{\"id\":\"b90cb710-eb2c-4547-9ad2-b0fef3108da6\",\"label\":\"区域药店\"}],\"direct-presentation\":false}"}
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"},{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"},{\"id\":\"b90cb710-eb2c-4547-9ad2-b0fef3108da6\",\"label\":\"区域药店\"}],\"direct-presentation\":false}"}
    Given 结束批量更新元数据
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    Given 删除object_validations表中id为6f56fed3-d54a-11e9-b9aa-0298a739d3e8的数据
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"区域医生"
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    Given 当数据不存在时，在数据表object_validations中插入数据，字段值如下
      | id                                   | object_id                            | label  | description | type    | formula | trigger_code                                                                                                                                                                                                                                                                                                                                                                                                                                    | error_message | deleted | created_by | created_on          | modified_by | modified_on         | edit_type | is_active | source | name            | listens | version | sys_created_on      | sys_modified_on     |
      | 6f56fed3-d54a-11e9-b9aa-0298a739d3e8 | 5299a45e-7408-11e6-bbe0-5cc5d4b571d2 | 拜访验证规则 | 拜访验证规则      | trigger | null    | from orionbase.common import models\nfrom django.utils.timezone import utc, localtime\nimport datetime\n\ncalls = models.Calls.objects.filter(deleted=delete_types.is_not_delete, account_id=instance.account.id, call_date=instance.call_date, owner_id=instance.owner.id)\ncall = calls[0] if calls else None\n\nif call and call.id != instance.id:\n message = '每人只可以在同一天内对同一医生拜访一次'\n raise create_validation_trigger_rule_errors(message) |               | 0       | 1          | 2019-09-16 11:22:20 | 1           | 2023-02-10 12:34:47 | pre_save  | 1         | custom | call_validation | null    | null    | 2019-09-16 11:22:20 | 2023-02-10 12:34:47 |
    Given "admin""<EMAIL>"登录online admin系统
    And 调用缓存管理API清除系统缓存
    And 用户登出online admin系统
    And 用户等待10秒
    And 在安卓设备点击"播放资料"
    And 在安卓设备点击"区域医生"
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    And 在安卓设备播放电子资料页面展开按钮
    And 等待0.5秒

    And 在安卓设备点击"保存"
    Then 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
      | 每人只可以在同一天内对同一医生拜访一次 |

  @ui @wechatwork @Android @Android_540191996 @no_daily @Jesse @Merck
  Scenario: 【Merck】item_play_doc/play_doc只配置区域医院，直接进入医院list
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"}],\"direct-presentation\":false}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"}],\"direct-presentation\":false}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    Given 结束批量更新元数据
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 代表搜索医生"中国医学科学院肿瘤医院"
    Then 代表点击医生"中国医学科学院肿瘤医院"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生        | 拜访日期    | 关键信息                                                     |
      | 中国医学科学院肿瘤医院 | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
#    And 返回sfa企业微信首页
    And 返回sfa企业微信首页

    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 代表搜索医生"中国医学科学院肿瘤医院"
    Then 代表点击医生"中国医学科学院肿瘤医院"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生        | 拜访日期    | 关键信息                                                     |
      | 中国医学科学院肿瘤医院 | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页

  @ui @wechatwork @Android @Android_540191997 @no_daily @Jesse @Merck
  Scenario: 【Merck】item_play_doc/play_doc只配置区域医生，直接进入医生list
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"}],\"direct-presentation\":false}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"}],\"direct-presentation\":false}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    Given 结束批量更新元数据
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                     |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    And 在安卓设备点击"会后拜访"
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                     |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页

  @ui @wechatwork @Android @Android_540191998 @no_daily @Jesse @Merck
  Scenario: 【Merck】两个btn均不配置，报错“当前用户没有分配可用的主数据类型”
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[],\"direct-presentation\":false}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[],\"direct-presentation\":false}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    Given 结束批量更新元数据
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
      | 当前用户没有分配可用的主数据类型 |
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
      | 当前用户没有分配可用的主数据类型 |


  @ui @wechatwork @Android @Android_540191999 @no_daily @Jesse @Merck
  Scenario: 【Merck】"direct-presentation": true，只配置区域医院=>先播放后直接进入医院list
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"}],\"direct-presentation\":true}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a223ef9e-5f7e-11eb-9436-0239c10f5280\",\"label\":\"区域医院\"}],\"direct-presentation\":true}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    Given 结束批量更新元数据
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 代表搜索医生"中国医学科学院肿瘤医院"
    Then 代表点击医生"中国医学科学院肿瘤医院"卡片进入医生详情
    And 在安卓设备点击"会后拜访"
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生        | 拜访日期    | 关键信息                                                     |
      | 中国医学科学院肿瘤医院 | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 代表搜索医生"中国医学科学院肿瘤医院"
    Then 代表点击医生"中国医学科学院肿瘤医院"卡片进入医生详情
    And 在安卓设备点击"会后拜访"
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生        | 拜访日期    | 关键信息                                                     |
      | 中国医学科学院肿瘤医院 | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页

  @ui @wechatwork @Android @Android_540192000 @no_daily @Jesse @Merck
  Scenario: 【Merck】"direct-presentation": true，只配置区域医生=>先播放后直接进入医生list
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"}],\"direct-presentation\":true}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[{\"id\":\"a8cc414f-4fa8-4292-b8e8-59f14ecb360d\",\"label\":\"区域医生\"}],\"direct-presentation\":true}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    Given 结束批量更新元数据
    Given 设置custom setting
      | key                      | value                                |
      | default_call_record_type | {"professional": "", "hospital": ""} |
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备点击"会后拜访"
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                     |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 代表搜索医生"{hcp1_account_name}"
    Then 代表点击医生"{hcp1_account_name}"卡片进入医生详情
    And 在安卓设备点击"会后拜访"
    Then 验证电子资料详情页面
      | 描述   | auto-多类型混合-SFA |
      | 相关产品 | 压氏立7片;立文降压14片  |
      | 性质   | 私有             |
    And 在安卓设备点击"查看拜访"
    Then 验证拜访报告详情
      | 拜访医生                | 拜访日期    | 关键信息                                                     |
      | {hcp1_account_name} | {today} | 立文降压14片::立文降压14片key msg - auto;压氏立7片::要测一下关键信息能不能工作-Auto |
    And 返回sfa企业微信首页


  @ui @wechatwork @Android @Android_540192001 @no_daily @Jesse @Merck
  Scenario: 【Merck】"direct-presentation": true，不配置医院和医生=>先播放后报错“当前用户没有分配可用的主数据类型”
    ### 在电子资料列表页和详情页播放资料按钮上配置快速播放和account record type
    Given 开始批量更新元数据
    Given 按如下条件{"page_list_id":"bac79419-de55-43fc-8b8d-3c7ed809b41a","label":"播放资料"}更新表page_list_actions中的字段值{"configurations":"{\"account-types-filters\":[],\"direct-presentation\":true}"}
    Given 按如下条件{"page_layout_id":"d7a22b11-1b7b-4f9f-9df8-50c062ab235d","label":"播放资料"}更新表page_layout_actions中的字段值{"configurations":"{\"account-types-filters\":[],\"direct-presentation\":true}"}
    Given 按如下条件{"key":"eda_need_feedback"}更新表custom_settings中的字段值{"value":"true"}
    ### 这里的value比较奇葩就是这样勿改
    Given 按如下条件{"key":"auto_submit_call_report_after_eda"}更新表custom_settings中的字段值{"value":"\"1\""}
    Given 结束批量更新元数据
    When 清理医生{hcp1_account_id}阅读电子资料信息
    When 系统切换到安卓设备 "androidDevice_1_wechat"
    Then 医生1打开微信添加医药代表1到我的企业微信联系人
    When 恢复医生绑定信息微信昵称{hcp1_nick_name}
    When 系统切换到安卓设备 "androidDevice_1_wechatwork"
    Then 医药代表从企业微信端打开SFA应用
    Then 系统切换到webview界面
    Then 医药代表从sfa企业微信端页面点击"电子资料管理"
    And 代表搜索电子资料"auto-多类型混合-SFA"
    And 在安卓设备点击"播放资料"
    And 在安卓设备弹窗点击"取消"如果弹窗包含以下信息
      | 是否继续上次讲解 |
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否放弃上次讲解 |
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
      | 当前用户没有分配可用的主数据类型 |
    And 返回前一页
    And 在安卓设备弹窗点击"确定"如果弹窗包含以下信息
      | 是否退出当前讲解？ |
    And 代表搜索电子资料"auto-多类型混合-SFA"
    Then 代表点击电子资料"auto-多类型混合-SFA"卡片进入电子资料详情
    And 在安卓设备点击"播放资料"
    Then 验证多类型混合电子资料显示左右翻页按钮和页码
    Then 在播放EDA页面保存拜访报告
    And 在安卓设备弹窗点击"确定"验证弹窗包含以下信息
      | 当前用户没有分配可用的主数据类型 |