import sys
import pandas as pd
import json

# Pass the results JSON file as arguments
# Example: python3 to_excel.py results.json
results_file = sys.argv[1]

# Read results JSON file
with open(results_file, 'r') as file:
    results = json.load(file)

outer_suite_list = results['suites']

# Read mapping JSON file
with open('mapping.json', 'r') as file:
    mapping = json.load(file)

# Reverse the mapping for quick lookup
reverse_mapping = {}
for key, value in mapping.items():
    for object_name in value:
        reverse_mapping[object_name] = key

# Filter failed cases
failed_case_list = []
for outer_suite in outer_suite_list:
    # Skip common tests
    if outer_suite['title'].startswith('common'):
        continue
    
    # Get object name from splitting the title
    if '/' in outer_suite['title']:
        object_name = outer_suite['title'].split('/')[1]

    # Get squad from reverse mapping if object name exists
    squad = reverse_mapping.get(object_name, 'Unknown')
       
    for spec in outer_suite['specs']:
        if not spec['ok']:
            failed_case = {}
            failed_case['Case'] = spec['title']
            failed_case['Project'] = spec['tests'][0]['projectId']
            failed_case['Squad'] = squad
            failed_case_list.append(failed_case)

    if 'suites' in outer_suite:
        for suite in outer_suite['suites']:
            suite_title = suite['title']
            for spec in suite['specs']:
                if not spec['ok']:
                    failed_case = {}
                    failed_case['Case'] = suite_title + ' > ' + spec['title']
                    failed_case['Project'] = spec['tests'][0]['projectId']
                    failed_case['Squad'] = squad
                    failed_case_list.append(failed_case)

# Sort the failed cases by Squad and Case
failed_case_list = sorted(failed_case_list, key=lambda x: (x['Squad'], x['Case']))

# Create a DataFrame from the failed cases
df = pd.DataFrame(failed_case_list)

# Export the DataFrame to an Excel file
df.to_excel('failed_cases.xlsx', index=False)
