{"cookies": [{"name": "sessionid", "value": "svyzgxvn9p9sl1jh40falorqwr5r1lre", "domain": "localhost", "path": "/", "expires": 1711012581.693534, "httpOnly": true, "secure": true, "sameSite": "Lax"}], "origins": [{"origin": "http://localhost:8009", "localStorage": [{"name": "ls.username", "value": "\"<EMAIL>\""}, {"name": "ls.language", "value": "{}"}, {"name": "ls.is_login", "value": "true"}, {"name": "ls.position_id", "value": "523"}, {"name": "ls.X_CLIENT_VERSION", "value": "\"2024-03-20T10:27:38.868Z\""}, {"name": "localTriggerTime", "value": "{}"}, {"name": "ls.orion_user_id", "value": "765"}, {"name": "ls.water<PERSON><PERSON><PERSON>", "value": "{\"show_time\":false,\"text\":\"QA SFE专员9\"}"}, {"name": "timerMap", "value": "{}"}, {"name": "ls.current_position", "value": "{\"id\":\"523\",\"name\":\"SFE专员9\"}"}, {"name": "ls.request_chain", "value": "[{\"apiUrl\":\"/login/\",\"requestId\":\"39213f6928314147b1072506bc7f8a98\",\"path\":\"\"},{\"apiUrl\":\"/api/company-settings/?screen-size=large\",\"requestId\":\"b0e6e3b86ed34c69a36674b02e8f2345\",\"path\":\"\"},{\"apiUrl\":\"/api/base-info/\",\"requestId\":\"cdf9d7840c4f41c89972de095a739c22\",\"path\":\"/home\"},{\"apiUrl\":\"/api/menus/\",\"requestId\":\"5c9eeacb41b94a55bfbff714649db470\",\"path\":\"\"},{\"apiUrl\":\"/api/notice-board/display-list/\",\"requestId\":\"e746466ff1c04f18a285031577595d55\",\"path\":\"\"},{\"apiUrl\":\"/api/approval/type/?name=approval_pending\",\"requestId\":\"eb117cd0dc9b4d979b9a841fdf8fee8c\",\"path\":\"\"},{\"apiUrl\":\"/api/approval/batch/status/\",\"requestId\":\"7a0dc0fdaccd48ec922e8b8f0372935d\",\"path\":\"\"},{\"apiUrl\":\"/api/approval/tab/\",\"requestId\":\"e627d2604fcd4087935a72d38c082e9f\",\"path\":\"\"}]"}, {"name": "ls.state_id", "value": "\"aac97c40-03d8-11e7-af86-68f72882c9d0\""}, {"name": "ls.position_name", "value": "\"SFE专员9\""}, {"name": "ls.positions", "value": "{\"current_position\":{\"id\":\"523\",\"name\":\"SFE专员9\"},\"position_list\":[{\"id\":\"523\",\"name\":\"SFE专员9\"}]}"}, {"name": "ls.remember_user", "value": "true"}, {"name": "ls.app_name", "value": "\"Veeva CRM\""}, {"name": "ls.role_log_in_as_delegate", "value": "false"}, {"name": "ls.user_id", "value": "368"}, {"name": "exit_relogin", "value": "N"}, {"name": "ls.login_username", "value": "\"<EMAIL>\""}, {"name": "sessionStartTime", "value": "1711010781505"}]}]}