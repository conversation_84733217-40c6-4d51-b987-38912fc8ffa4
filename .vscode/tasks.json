{"version": "2.0.0", "tasks": [{"label": "全量 dl", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "@Merck,@Mundi --tags=@no_daily --tags=-@QA_XiaoleiSong", "--env", "dl"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "dl <PERSON> Path", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "@Merck,@Mundi --tags=@no_daily --tags=@happy_path --tags=-@QA_XiaoleiSong", "--env", "dl"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "重跑 dl", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "--rerun", "--env", "dl"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "全量 znn", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "@Abbvie --tags=@no_daily --tags=-@QA_XiaoleiSong", "--env", "znn", "-group2"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "znn Happy Path", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "@Abbvie --tags=@no_daily --tags=@happy_path --tags=-@QA_XiaoleiSong", "--env", "znn", "-group2"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "重跑 znn", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "--rerun", "--env", "znn", "-group2"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "<PERSON>t msd", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "Android_540330790", "--env", "msd", "-q", "--debug"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "真机", "type": "shell", "command": "${workspaceFolder}/venv39arm/bin/python", "args": ["${workspaceFolder}/watt/run_behave_local_rtw.py", "-c", "rtw", "-t", "Android_536545397", "--env", "dl", "-group3"], "options": {"cwd": "${workspaceFolder}/watt"}, "group": "test", "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}]}