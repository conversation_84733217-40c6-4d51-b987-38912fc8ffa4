---
description: 
globs: 
alwaysApply: false
---
# 项目结构指南

本项目主要包括以下目录和文件：

## 根目录

- [README.md](mdc:README.md)：项目总览及使用说明。
- [Dockerfile](mdc:Dockerfile)：定义自动化镜像构建。
- [docker-compose.yml](mdc:docker-compose.yml)：服务编排配置。
- [Makefile](mdc:Makefile)：
  - `make build`：构建镜像。
  - `make up`：启动服务。
  - `make down`：停止并移除服务。
- [.gitlab-ci.yml](mdc:.gitlab-ci.yml)：CI/CD 配置。
- [rerun](mdc:rerun)：失败用例重跑脚本或配置。

## 重要目录

- `.cursor/rules/`：存放 Cursor 交互规则文件。
- `watt/`：基于 Behave 和 unittest 的自动化测试框架：
  - `watt/cfg.ini`：Behave 测试配置。
  - `watt/requirements.txt`：测试依赖。
  - `watt/run_behave_local.py`：本地运行 Behave 测试。
  - `watt/run_behave_tests.py`：执行所有 Behave 测试。
  - `watt/run_tests.py`：执行综合测试。
  - `watt/parallel_run_behave.py`、`watt/parallel_run_unittest.py`：并行执行测试。
- `rest_api_test/`：REST API 自动化测试脚本。
- `playwright/`：Playwright 自动化测试脚本。
- `security_scan/`：安全扫描脚本和配置。
- `report/`、`allure-results/`：测试报告及结果。
- `scripts/`：各类辅助脚本。
- `rtw/`：运行时脚本。

## BDD 测试相关

- `.feature` 文件：分布在 [watt/testscripts/BDD](mdc:watt/testscripts/BDD) 及其各子目录中，文件后缀为 `.feature`。
- 步骤定义：位于各子目录下的 `steps` 文件夹，如 `watt/testscripts/BDD/approval/steps/*.py`，以及公共步骤文件 [common.py](mdc:watt/testscripts/BDD/common.py)、[common_step_db.py](mdc:watt/testscripts/BDD/common_step_db.py)、[common_product.py](mdc:watt/testscripts/BDD/common_product.py)。

在 VSCode 中，若使用 `cucumberautocomplete` 插件，可配置：

```json
"cucumberautocomplete.steps": [
    "watt/testscripts/BDD/**/steps/*.py",
    "watt/testscripts/BDD/common_product.py",
    "watt/testscripts/BDD/common_step_db.py",
    "watt/testscripts/BDD/common.py"
],
"cucumberautocomplete.syncfeatures": "watt/testscripts/BDD/*feature"
```

- 查找 `.feature` 文件时，优先在 `watt/testscripts/BDD` 目录下搜索。
- 查找步骤定义时，优先在 `watt/testscripts/BDD/**/steps` 目录和公共步骤文件中搜索。

## 常用命令

```bash
# 构建镜像
make build

# 启动服务
make up

# 停止并移除服务
make down

# 本地运行 Behave 测试
python3 watt/run_behave_local.py

# 执行所有 Behave 测试
python3 watt/run_behave_tests.py

# 执行综合测试
python3 watt/run_tests.py

# 并行运行 Behave 测试
python3 watt/parallel_run_behave.py

# 并行运行单元测试
python3 watt/parallel_run_unittest.py
```

## 代码导航提示

- 使用 `[文件名](mdc:相对路径)` 方式快速跳转到对应文件。
- 查找测试用例时，优先在 `watt/` 目录下定位以 `run_` 开头的脚本。
