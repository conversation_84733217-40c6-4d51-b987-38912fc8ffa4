#!/usr/bin/env node
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { 
  CallToolRequestSchema,
  ListToolsRequestSchema
} from "@modelcontextprotocol/sdk/types.js";
import mysql from 'mysql2/promise';

// Define database configurations
interface DbConfig {
  host: string;
  db: string;
  port: number;
}

const dbConfigs: { [key: string]: DbConfig } = {
  "QACommondl": {
    host: "127.0.0.1",
    db: "orion",
    port: 3307,
  },
  "QACommongq": {
    host: "127.0.0.1",
    db: "orion",
    port: 3308,
  },
  "QACommonznn": {
    host: "127.0.0.1",
    db: "orion",
    port: 3309,
  },
  "QACommonBehave": {
    host: "dev-sfa-mysql-1.cmeep8hwwlid.rds.cn-north-1.amazonaws.com.cn",
    db: "QACommonBehave",
    port: 3306,
  },
};

// Common username and password (as specified by the user)
const COMMON_USERNAME = "veevaapp"; // Placeholder: user should replace with actual username
const COMMON_PASSWORD = "ECJC5f4gnwK6Q6Wr"; // Placeholder: user should replace with actual password

const server = new Server({
  name: "mysql-db-server",
  version: "0.1.0"
}, {
  capabilities: {
    tools: {}
  }
});

async function createConnection(config: DbConfig) {
  return await mysql.createConnection({
    host: config.host,
    user: COMMON_USERNAME,
    password: COMMON_PASSWORD,
    database: config.db,
    port: config.port,
  });
}

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: "execute_sql",  
        description: "Execute SQL query on MySQL database",
        inputSchema: {
          type: "object",
          properties: {
            env_name: {
              type: "string",
              enum: ["QACommondl", "QACommongq", "QACommonznn", "QACommonBehave"],
              description: "Database environment name"
            },
            query: {
              type: "string", 
              description: "SQL query to execute"
            }
          },
          required: ["env_name", "query"]
        }
      }
    ]
  };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  if (name === "execute_sql") {
    const { env_name, query } = args as { env_name: string; query: string };
    
    const config = dbConfigs[env_name];
    if (!config) {
      return {
        content: [{
          type: "text",
          text: `Error: Unknown environment ${env_name}`
        }],
        isError: true
      };
    }

    try {
      const connection = await createConnection(config);
      const [rows] = await connection.execute(query);
      await connection.end();
      
      return {
        content: [{
          type: "text", 
          text: JSON.stringify(rows, null, 2)
        }]
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      return {
        content: [{
          type: "text",
          text: `Database error: ${errorMessage}`
        }],
        isError: true
      };
    }
  }
  
  throw new Error(`Unknown tool: ${name}`);
});

const transport = new StdioServerTransport();
await server.connect(transport);
console.error('MySQL DB MCP server running on stdio');
